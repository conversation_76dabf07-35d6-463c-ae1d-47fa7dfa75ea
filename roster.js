const { ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * DOM Elements
 */
const backToHomeBtn = document.getElementById('back-to-home');
const classNameEl = document.getElementById('class-name');
const classDateEl = document.getElementById('class-date').querySelector('span');
const classTimeEl = document.getElementById('class-time').querySelector('span');
const athleteCountEl = document.getElementById('athlete-count').querySelector('span');
const rosterGrid = document.getElementById('roster-grid');
const emptyRoster = document.getElementById('empty-roster');
const loadingIndicator = document.getElementById('loading');

// Modal elements
const addAthleteModal = document.getElementById('add-athlete-modal');
const addExistingModal = document.getElementById('add-existing-modal');
const addNewAthleteBtn = document.getElementById('add-new-athlete');
const addExistingAthleteBtn = document.getElementById('add-existing-athlete');
const addAthleteForm = document.getElementById('add-athlete-form');
const availableAthletesList = document.getElementById('available-athletes');
const athleteSearch = document.getElementById('athlete-search');

/**
 * Application State
 */
let currentClass = null;
let currentAthletes = [];
let allAthletes = [];

/**
 * Initialize the roster page
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Roster page - Renderer process started');
    
    // Get class ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const classId = urlParams.get('classId');
    
    if (!classId) {
        showNotification('No class selected', 'error');
        goBackToHome();
        return;
    }
    
    // Set up event listeners
    setupEventListeners();
    
    // Load class and athlete data
    await loadClassData(classId);
    await loadAthleteData();
    
    // Initialize placeholder athletes if none exist
    await initializePlaceholderAthletes();
    
    // Render the roster
    renderRoster();
});

/**
 * Set up all event listeners
 */
function setupEventListeners() {
    // Navigation
    backToHomeBtn.addEventListener('click', goBackToHome);
    
    // Modal controls
    addNewAthleteBtn.addEventListener('click', () => showModal('add-athlete-modal'));
    addExistingAthleteBtn.addEventListener('click', async () => {
        showModal('add-existing-modal');
        await loadAvailableAthletes();
    });
    
    // Modal close buttons
    document.getElementById('close-add-modal').addEventListener('click', () => hideModal('add-athlete-modal'));
    document.getElementById('close-existing-modal').addEventListener('click', () => hideModal('add-existing-modal'));
    document.getElementById('cancel-add').addEventListener('click', () => hideModal('add-athlete-modal'));
    
    // Form submission
    addAthleteForm.addEventListener('submit', handleAddNewAthlete);
    
    // Search functionality
    athleteSearch.addEventListener('input', filterAvailableAthletes);
    
    // Close modals when clicking outside
    addAthleteModal.addEventListener('click', (e) => {
        if (e.target === addAthleteModal) hideModal('add-athlete-modal');
    });
    addExistingModal.addEventListener('click', (e) => {
        if (e.target === addExistingModal) hideModal('add-existing-modal');
    });
}

/**
 * Load class data from storage
 */
async function loadClassData(classId) {
    showLoading(true);
    
    try {
        const classes = await ipcRenderer.invoke('load-classes');
        currentClass = classes.find(c => c.id === classId);
        
        if (!currentClass) {
            showNotification('Class not found', 'error');
            goBackToHome();
            return;
        }
        
        // Update UI with class information
        classNameEl.textContent = currentClass.name;
        classDateEl.textContent = formatDate(currentClass.date);
        classTimeEl.textContent = formatTime(currentClass.time);
        
    } catch (error) {
        console.error('Error loading class data:', error);
        showNotification('Failed to load class data', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Load athlete data from storage
 */
async function loadAthleteData() {
    try {
        allAthletes = await ipcRenderer.invoke('load-athletes');

        // Clean up any duplicate athletes
        allAthletes = removeDuplicateAthletes(allAthletes);

        // Filter athletes for current class
        if (currentClass) {
            currentAthletes = allAthletes.filter(athlete =>
                athlete.classIds && athlete.classIds.includes(currentClass.id)
            );

            // Update athlete count
            athleteCountEl.textContent = `${currentAthletes.length} Athletes`;
        }

    } catch (error) {
        console.error('Error loading athlete data:', error);
        showNotification('Failed to load athlete data', 'error');
    }
}

/**
 * Remove duplicate athletes from the array
 */
function removeDuplicateAthletes(athletes) {
    const uniqueAthletes = [];
    const seenIds = new Set();
    const seenNames = new Map();

    for (const athlete of athletes) {
        // Check for duplicate IDs
        if (seenIds.has(athlete.id)) {
            console.warn(`Duplicate athlete ID found: ${athlete.id}`);
            continue;
        }

        // Check for duplicate names and merge class IDs
        const nameKey = athlete.name.toLowerCase();
        if (seenNames.has(nameKey)) {
            const existingAthlete = seenNames.get(nameKey);
            // Merge class IDs
            if (athlete.classIds) {
                if (!existingAthlete.classIds) {
                    existingAthlete.classIds = [];
                }
                athlete.classIds.forEach(classId => {
                    if (!existingAthlete.classIds.includes(classId)) {
                        existingAthlete.classIds.push(classId);
                    }
                });
            }
            console.warn(`Duplicate athlete name found: ${athlete.name}, merging class IDs`);
            continue;
        }

        seenIds.add(athlete.id);
        seenNames.set(nameKey, athlete);
        uniqueAthletes.push(athlete);
    }

    // If we removed duplicates, save the cleaned data
    if (uniqueAthletes.length !== athletes.length) {
        console.log(`Removed ${athletes.length - uniqueAthletes.length} duplicate athletes`);
        // Save cleaned data asynchronously
        ipcRenderer.invoke('save-athletes', uniqueAthletes).catch(error => {
            console.error('Error saving cleaned athlete data:', error);
        });
    }

    return uniqueAthletes;
}

/**
 * Initialize placeholder athletes for demonstration
 */
async function initializePlaceholderAthletes() {
    if (allAthletes.length === 0) {
        const placeholderAthletes = [
            {
                id: 'athlete-1',
                name: 'Jordan Kim',
                nickname: 'Flash',
                age: 12,
                level: 'intermediate',
                description: 'Fast climber with excellent balance',
                sessionsAttended: 8,
                classIds: [currentClass.id]
            },
            {
                id: 'athlete-2',
                name: 'Alex Rivera',
                nickname: 'Shadow',
                age: 10,
                level: 'beginner',
                description: 'New to ninja training, very enthusiastic',
                sessionsAttended: 3,
                classIds: [currentClass.id]
            },
            {
                id: 'athlete-3',
                name: 'Sam Chen',
                nickname: 'Lightning',
                age: 14,
                level: 'advanced',
                description: 'Experienced athlete with strong technique',
                sessionsAttended: 15,
                classIds: [currentClass.id]
            },
            {
                id: 'athlete-4',
                name: 'Taylor Johnson',
                nickname: 'Rocket',
                age: 11,
                level: 'intermediate',
                description: 'Great at obstacle courses',
                sessionsAttended: 6,
                classIds: [currentClass.id]
            },
            {
                id: 'athlete-5',
                name: 'Morgan Davis',
                nickname: 'Stealth',
                age: 13,
                level: 'beginner',
                description: 'Quiet but determined, improving quickly',
                sessionsAttended: 4,
                classIds: [currentClass.id]
            }
        ];
        
        // Save placeholder athletes
        await ipcRenderer.invoke('save-athletes', placeholderAthletes);
        allAthletes = placeholderAthletes;
        currentAthletes = placeholderAthletes;
        
        // Update athlete count
        athleteCountEl.textContent = `${currentAthletes.length} Athletes`;
    }
}

/**
 * Render the athlete roster
 */
function renderRoster() {
    if (currentAthletes.length === 0) {
        // Show placeholder athletes instead of empty state
        renderPlaceholderAthletes();
        return;
    }

    rosterGrid.style.display = 'grid';
    emptyRoster.classList.add('hidden');
    
    rosterGrid.innerHTML = currentAthletes.map(athlete => `
        <div class="athlete-card" data-athlete-id="${athlete.id}">
            <div class="athlete-header">
                <div class="athlete-avatar">
                    ${getAthleteInitials(athlete.name)}
                </div>
                <div class="athlete-info">
                    <h3>${escapeHtml(athlete.name)}</h3>
                    ${athlete.nickname ? `<div class="athlete-nickname">"${escapeHtml(athlete.nickname)}"</div>` : ''}
                </div>
            </div>
            
            <div class="athlete-details">
                <div class="athlete-description">
                    ${athlete.description ? escapeHtml(athlete.description) : 'No description available'}
                </div>
                
                <div class="athlete-stats">
                    <span class="level-badge level-${athlete.level || 'beginner'}">
                        ${athlete.level ? athlete.level.charAt(0).toUpperCase() + athlete.level.slice(1) : 'No Level Set'}
                    </span>
                    <span class="sessions-count">
                        <i class="fas fa-calendar-check"></i>
                        ${athlete.sessionsAttended || 0} Sessions
                    </span>
                </div>
            </div>
            
            <div class="athlete-actions">
                <span class="athlete-age">
                    <i class="fas fa-birthday-cake"></i>
                    Age ${athlete.age}
                </span>
                <button class="btn btn-remove" onclick="removeAthlete('${athlete.id}')">
                    <i class="fas fa-times"></i>
                    Remove
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * Render placeholder athletes when no real athletes are enrolled
 */
function renderPlaceholderAthletes() {
    rosterGrid.style.display = 'grid';
    emptyRoster.classList.add('hidden');

    const placeholderAthletes = [
        {
            name: 'Sam Chen',
            nickname: 'Lightning',
            age: 14,
            level: 'advanced',
            description: 'Loves warped wall and speed challenges',
            avatar: 'SC'
        },
        {
            name: 'Maya Rodriguez',
            nickname: 'Shadow',
            age: 12,
            level: 'intermediate',
            description: 'Excellent balance and stealth movements',
            avatar: 'MR'
        },
        {
            name: 'Alex Kim',
            nickname: 'Flash',
            age: 13,
            level: 'advanced',
            description: 'Speed demon with incredible agility',
            avatar: 'AK'
        },
        {
            name: 'Jordan Taylor',
            nickname: 'Rocket',
            age: 11,
            level: 'beginner',
            description: 'New to ninja training but very determined',
            avatar: 'JT'
        },
        {
            name: 'Casey Morgan',
            nickname: 'Viper',
            age: 15,
            level: 'intermediate',
            description: 'Master of precision and technique',
            avatar: 'CM'
        }
    ];

    rosterGrid.innerHTML = placeholderAthletes.map(athlete => `
        <div class="athlete-card placeholder-card">
            <div class="athlete-header">
                <div class="athlete-avatar placeholder-avatar">
                    ${athlete.avatar}
                </div>
                <div class="athlete-info">
                    <h3>${athlete.name}</h3>
                    <div class="athlete-nickname">"${athlete.nickname}"</div>
                </div>
            </div>

            <div class="athlete-details">
                <div class="athlete-description">
                    ${athlete.description}
                </div>

                <div class="athlete-stats">
                    <span class="level-badge level-${athlete.level}">
                        ${athlete.level.charAt(0).toUpperCase() + athlete.level.slice(1)}
                    </span>
                    <span class="sessions-count">
                        <i class="fas fa-calendar-check"></i>
                        Sample Data
                    </span>
                </div>
            </div>

            <div class="athlete-actions">
                <span class="athlete-age">
                    <i class="fas fa-birthday-cake"></i>
                    Age ${athlete.age}
                </span>
                <div class="placeholder-badge">
                    <i class="fas fa-eye"></i>
                    Preview
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Get athlete initials for avatar
 */
function getAthleteInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

/**
 * Handle adding new athlete
 */
async function handleAddNewAthlete(e) {
    e.preventDefault();

    const formData = new FormData(addAthleteForm);
    const athleteName = formData.get('name').trim();

    // Check if athlete with same name already exists
    const existingAthlete = allAthletes.find(a =>
        a.name.toLowerCase() === athleteName.toLowerCase()
    );

    if (existingAthlete) {
        // If athlete exists, add them to current class instead
        if (existingAthlete.classIds && existingAthlete.classIds.includes(currentClass.id)) {
            showNotification('An athlete with this name is already enrolled in this class', 'error');
            return;
        } else {
            // Add existing athlete to current class
            if (!existingAthlete.classIds) {
                existingAthlete.classIds = [];
            }
            existingAthlete.classIds.push(currentClass.id);
            currentAthletes.push(existingAthlete);

            // Save and update UI
            await ipcRenderer.invoke('save-athletes', allAthletes);
            renderRoster();
            athleteCountEl.textContent = `${currentAthletes.length} Athletes`;
            addAthleteForm.reset();
            hideModal('add-athlete-modal');
            showNotification(`Existing athlete "${athleteName}" added to class!`, 'success');
            return;
        }
    }

    const newAthlete = {
        id: 'athlete-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
        name: athleteName,
        age: parseInt(formData.get('age')),
        nickname: formData.get('nickname').trim(),
        level: formData.get('level'),
        description: formData.get('description').trim(),
        sessionsAttended: 0,
        classIds: [currentClass.id]
    };

    // Validate required fields
    if (!newAthlete.name || !newAthlete.age || !newAthlete.level) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    showLoading(true);

    try {
        // Add to all athletes
        allAthletes.push(newAthlete);
        currentAthletes.push(newAthlete);

        // Save to storage
        await ipcRenderer.invoke('save-athletes', allAthletes);

        // Update UI
        renderRoster();
        athleteCountEl.textContent = `${currentAthletes.length} Athletes`;

        // Reset form and close modal
        addAthleteForm.reset();
        hideModal('add-athlete-modal');

        showNotification('New athlete added successfully!', 'success');

    } catch (error) {
        console.error('Error adding athlete:', error);
        showNotification('Failed to add athlete', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Load available athletes for adding to class
 */
async function loadAvailableAthletes() {
    console.log('Loading available athletes...');

    // Ensure we have the latest athlete data
    try {
        allAthletes = await ipcRenderer.invoke('load-athletes');
        console.log(`Loaded ${allAthletes.length} total athletes from database`);
    } catch (error) {
        console.error('Error loading athletes:', error);
        showNotification('Failed to load athlete data', 'error');
        return;
    }

    // Filter athletes who are NOT already enrolled in the current class
    const availableAthletes = allAthletes.filter(athlete => {
        // Include athletes who have no classIds array or don't include current class
        const isNotInClass = !athlete.classIds || !athlete.classIds.includes(currentClass.id);
        console.log(`Athlete ${athlete.name}: classIds = ${JSON.stringify(athlete.classIds)}, isNotInClass = ${isNotInClass}`);
        return isNotInClass;
    });

    console.log(`Found ${availableAthletes.length} available athletes for class ${currentClass.id}`);

    if (availableAthletes.length === 0) {
        availableAthletesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>No Available Athletes</h3>
                <p>All athletes are already enrolled in this class, or no athletes exist in the system.</p>
                <p><small>Create new athletes from the Home page to add them here.</small></p>
            </div>
        `;
        return;
    }

    // Sort athletes alphabetically by name for better UX
    availableAthletes.sort((a, b) => a.name.localeCompare(b.name));

    availableAthletesList.innerHTML = availableAthletes.map(athlete => {
        // Show which classes the athlete is currently enrolled in
        const enrolledClasses = athlete.classIds ? athlete.classIds.length : 0;

        return `
            <div class="available-athlete" data-athlete-id="${athlete.id}">
                <div class="available-athlete-info">
                    <div class="available-athlete-avatar">
                        ${getAthleteInitials(athlete.name)}
                    </div>
                    <div class="athlete-details">
                        <strong>${escapeHtml(athlete.name)}</strong>
                        ${athlete.nickname ? `<br><em>"${escapeHtml(athlete.nickname)}"</em>` : ''}
                        <br><small>Age ${athlete.age} • ${athlete.level || 'No level set'}</small>
                        <br><small class="enrollment-info">
                            <i class="fas fa-graduation-cap"></i>
                            Enrolled in ${enrolledClasses} class${enrolledClasses !== 1 ? 'es' : ''}
                        </small>
                    </div>
                </div>
                <button class="btn btn-add-existing" onclick="addExistingAthlete('${athlete.id}')"
                        title="Add ${escapeHtml(athlete.name)} to this class">
                    <i class="fas fa-plus"></i>
                    Add to Class
                </button>
            </div>
        `;
    }).join('');
}

/**
 * Filter available athletes based on search
 */
function filterAvailableAthletes() {
    const searchTerm = athleteSearch.value.toLowerCase();
    const athleteElements = availableAthletesList.querySelectorAll('.available-athlete');

    athleteElements.forEach(element => {
        const athleteText = element.textContent.toLowerCase();
        if (athleteText.includes(searchTerm)) {
            element.style.display = 'flex';
        } else {
            element.style.display = 'none';
        }
    });
}

/**
 * Add existing athlete to current class
 */
async function addExistingAthlete(athleteId) {
    showLoading(true);

    try {
        const athlete = allAthletes.find(a => a.id === athleteId);
        if (!athlete) {
            showNotification('Athlete not found', 'error');
            return;
        }

        // Check if athlete is already in this class
        if (athlete.classIds && athlete.classIds.includes(currentClass.id)) {
            showNotification('Athlete is already enrolled in this class', 'error');
            return;
        }

        // Add class ID to athlete's class list (prevent duplicates)
        if (!athlete.classIds) {
            athlete.classIds = [];
        }

        // Only add if not already present
        if (!athlete.classIds.includes(currentClass.id)) {
            athlete.classIds.push(currentClass.id);
        }

        // Update storage with the modified allAthletes array
        await ipcRenderer.invoke('save-athletes', allAthletes);

        // Update current athletes list (only if not already present)
        if (!currentAthletes.find(a => a.id === athleteId)) {
            currentAthletes.push(athlete);
        }

        // Update class athlete count
        await updateClassAthleteCount();

        // Update UI
        renderRoster();
        athleteCountEl.textContent = `${currentAthletes.length} Athletes`;
        await loadAvailableAthletes(); // Refresh available list

        // Close the modal
        hideModal('add-existing-modal');

        showNotification(`${athlete.name} added to class!`, 'success');

    } catch (error) {
        console.error('Error adding existing athlete:', error);
        showNotification('Failed to add athlete to class', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Remove athlete from current class
 */
async function removeAthlete(athleteId) {
    if (!confirm('Are you sure you want to remove this athlete from the class?')) {
        return;
    }

    showLoading(true);

    try {
        const athlete = allAthletes.find(a => a.id === athleteId);
        if (!athlete) {
            showNotification('Athlete not found', 'error');
            return;
        }

        // Remove class ID from athlete's class list
        if (athlete.classIds) {
            athlete.classIds = athlete.classIds.filter(id => id !== currentClass.id);
        }

        // Update storage
        await ipcRenderer.invoke('save-athletes', allAthletes);

        // Update current athletes list
        currentAthletes = currentAthletes.filter(a => a.id !== athleteId);

        // Update class athlete count
        await updateClassAthleteCount();

        // Update UI
        renderRoster();
        athleteCountEl.textContent = `${currentAthletes.length} Athletes`;

        showNotification('Athlete removed from class', 'success');

    } catch (error) {
        console.error('Error removing athlete:', error);
        showNotification('Failed to remove athlete', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Update the athlete count for the current class
 */
async function updateClassAthleteCount() {
    try {
        const classes = await ipcRenderer.invoke('load-classes');
        const classIndex = classes.findIndex(c => c.id === currentClass.id);

        if (classIndex !== -1) {
            classes[classIndex].athleteCount = currentAthletes.length;
            await ipcRenderer.invoke('save-classes', classes);
            console.log(`Updated athlete count for class ${currentClass.id} to ${currentAthletes.length}`);
        }
    } catch (error) {
        console.error('Error updating class athlete count:', error);
    }
}

/**
 * Utility Functions
 */

function goBackToHome() {
    window.location.href = 'index.html';
}

function showModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function hideModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function showLoading(show) {
    if (show) {
        loadingIndicator.classList.remove('hidden');
    } else {
        loadingIndicator.classList.add('hidden');
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Make functions available globally for onclick handlers
window.addExistingAthlete = addExistingAthlete;
window.removeAthlete = removeAthlete;
