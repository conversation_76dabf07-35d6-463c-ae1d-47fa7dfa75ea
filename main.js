const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;

// Keep a global reference of the window object
let mainWindow;

// Path to the classes data file
const CLASSES_FILE_PATH = path.join(__dirname, 'classes.json');

/**
 * Create the main application window
 */
function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // Optional: Add app icon
        title: 'Ninja Score Tracker'
    });

    // Load the index.html file
    mainWindow.loadFile('index.html');

    // Open DevTools in development (comment out for production)
    // mainWindow.webContents.openDevTools();

    // Handle window closed
    mainWindow.on('closed', function () {
        mainWindow = null;
    });
}

/**
 * Initialize the classes.json file if it doesn't exist
 */
async function initializeClassesFile() {
    try {
        await fs.access(CLASSES_FILE_PATH);
        console.log('Classes file exists');
    } catch (error) {
        // File doesn't exist, create it with empty array
        console.log('Creating classes file...');
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify([], null, 2));
    }
}

/**
 * Load classes from the JSON file
 */
async function loadClasses() {
    try {
        const data = await fs.readFile(CLASSES_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading classes:', error);
        return [];
    }
}

/**
 * Save classes to the JSON file
 */
async function saveClasses(classes) {
    try {
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify(classes, null, 2));
        console.log('Classes saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving classes:', error);
        return false;
    }
}

// App event handlers
app.whenReady().then(async () => {
    await initializeClassesFile();
    createWindow();

    app.on('activate', function () {
        // On macOS, re-create window when dock icon is clicked
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});

// Quit when all windows are closed
app.on('window-all-closed', function () {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for communication with renderer process
ipcMain.handle('load-classes', async () => {
    return await loadClasses();
});

ipcMain.handle('save-class', async (event, newClass) => {
    const classes = await loadClasses();
    
    // Add unique ID and creation timestamp
    newClass.id = Date.now().toString();
    newClass.createdAt = new Date().toISOString();
    newClass.athleteCount = 0; // Initialize athlete count
    
    classes.push(newClass);
    const success = await saveClasses(classes);
    
    if (success) {
        return { success: true, class: newClass };
    } else {
        return { success: false, error: 'Failed to save class' };
    }
});

ipcMain.handle('get-classes', async () => {
    return await loadClasses();
});

console.log('Ninja Score Tracker - Main process started');
