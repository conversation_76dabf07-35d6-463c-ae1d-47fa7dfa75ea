const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;

// Keep a global reference of the window object
let mainWindow;

// Path to the data files
const CLASSES_FILE_PATH = path.join(__dirname, 'classes.json');
const ATHLETES_FILE_PATH = path.join(__dirname, 'athletes.json');

/**
 * Create the main application window
 */
function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // Optional: Add app icon
        title: 'Ninja Score Tracker'
    });

    // Load the index.html file
    mainWindow.loadFile('index.html');

    // Open DevTools in development (comment out for production)
    // mainWindow.webContents.openDevTools();

    // Handle window closed
    mainWindow.on('closed', function () {
        mainWindow = null;
    });
}

/**
 * Initialize the data files if they don't exist
 */
async function initializeDataFiles() {
    // Initialize classes file
    try {
        await fs.access(CLASSES_FILE_PATH);
        console.log('Classes file exists');
    } catch (error) {
        console.log('Creating classes file...');
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify([], null, 2));
    }

    // Initialize athletes file
    try {
        await fs.access(ATHLETES_FILE_PATH);
        console.log('Athletes file exists');
    } catch (error) {
        console.log('Creating athletes file...');
        await fs.writeFile(ATHLETES_FILE_PATH, JSON.stringify([], null, 2));
    }
}

/**
 * Load classes from the JSON file
 */
async function loadClasses() {
    try {
        const data = await fs.readFile(CLASSES_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading classes:', error);
        return [];
    }
}

/**
 * Save classes to the JSON file
 */
async function saveClasses(classes) {
    try {
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify(classes, null, 2));
        console.log('Classes saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving classes:', error);
        return false;
    }
}

/**
 * Load athletes from the JSON file
 */
async function loadAthletes() {
    try {
        const data = await fs.readFile(ATHLETES_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading athletes:', error);
        return [];
    }
}

/**
 * Save athletes to the JSON file
 */
async function saveAthletes(athletes) {
    try {
        await fs.writeFile(ATHLETES_FILE_PATH, JSON.stringify(athletes, null, 2));
        console.log('Athletes saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving athletes:', error);
        return false;
    }
}

// App event handlers
app.whenReady().then(async () => {
    await initializeDataFiles();
    createWindow();

    app.on('activate', function () {
        // On macOS, re-create window when dock icon is clicked
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});

// Quit when all windows are closed
app.on('window-all-closed', function () {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for communication with renderer process
ipcMain.handle('load-classes', async () => {
    return await loadClasses();
});

ipcMain.handle('save-class', async (event, newClass) => {
    const classes = await loadClasses();
    
    // Add unique ID and creation timestamp
    newClass.id = Date.now().toString();
    newClass.createdAt = new Date().toISOString();
    newClass.athleteCount = 0; // Initialize athlete count
    
    classes.push(newClass);
    const success = await saveClasses(classes);
    
    if (success) {
        return { success: true, class: newClass };
    } else {
        return { success: false, error: 'Failed to save class' };
    }
});

ipcMain.handle('get-classes', async () => {
    return await loadClasses();
});

ipcMain.handle('save-classes', async (event, classes) => {
    const success = await saveClasses(classes);
    return { success };
});

// IPC handlers for athlete data
ipcMain.handle('load-athletes', async () => {
    return await loadAthletes();
});

ipcMain.handle('save-athletes', async (event, athletes) => {
    const success = await saveAthletes(athletes);
    return { success };
});

ipcMain.handle('get-athletes', async () => {
    return await loadAthletes();
});

console.log('Ninja Score Tracker - Main process started');
