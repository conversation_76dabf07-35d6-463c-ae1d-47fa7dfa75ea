const { ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * DOM Elements
 */
const navLinks = document.querySelectorAll('.nav-link');
const pages = document.querySelectorAll('.page');
const addClassForm = document.getElementById('add-class-form');
const classesList = document.getElementById('classes-list');
const loadingIndicator = document.getElementById('loading');

/**
 * Application State
 */
let currentClasses = [];

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Ninja Score Tracker - Renderer process started');
    
    // Set up navigation
    setupNavigation();
    
    // Set up form handling
    setupFormHandling();
    
    // Set today's date as default
    setDefaultDate();
    
    // Load existing classes
    await loadClasses();
});

/**
 * Set up navigation between pages
 */
function setupNavigation() {
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Remove active class from all links and pages
            navLinks.forEach(l => l.classList.remove('active'));
            pages.forEach(p => p.classList.remove('active'));
            
            // Add active class to clicked link
            link.classList.add('active');
            
            // Show corresponding page
            const pageId = link.getAttribute('data-page') + '-page';
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
        });
    });
}

/**
 * Set up form handling for adding new classes
 */
function setupFormHandling() {
    addClassForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(addClassForm);
        const newClass = {
            name: formData.get('className').trim(),
            date: formData.get('classDate'),
            time: formData.get('classTime'),
            description: formData.get('classDescription').trim()
        };
        
        // Validate required fields
        if (!newClass.name || !newClass.date || !newClass.time) {
            showNotification('Please fill in all required fields', 'error');
            return;
        }
        
        // Show loading
        showLoading(true);
        
        try {
            // Save class via IPC
            const result = await ipcRenderer.invoke('save-class', newClass);
            
            if (result.success) {
                // Add to local state
                currentClasses.push(result.class);
                
                // Update UI
                renderClasses();
                
                // Reset form
                addClassForm.reset();
                setDefaultDate();
                
                // Show success message
                showNotification('Class added successfully!', 'success');
            } else {
                showNotification('Failed to save class: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Error saving class:', error);
            showNotification('An error occurred while saving the class', 'error');
        } finally {
            showLoading(false);
        }
    });
}

/**
 * Set default date to today
 */
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('classDate').value = today;
}

/**
 * Load classes from storage
 */
async function loadClasses() {
    showLoading(true);
    
    try {
        currentClasses = await ipcRenderer.invoke('load-classes');
        renderClasses();
    } catch (error) {
        console.error('Error loading classes:', error);
        showNotification('Failed to load classes', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Render classes in the UI
 */
function renderClasses() {
    if (currentClasses.length === 0) {
        classesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <p style="color: #666; font-size: 1.1rem;">No classes yet. Add your first class above!</p>
            </div>
        `;
        return;
    }
    
    // Sort classes by date and time (newest first)
    const sortedClasses = [...currentClasses].sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateB - dateA;
    });
    
    classesList.innerHTML = sortedClasses.map(classItem => `
        <div class="class-card" data-class-id="${classItem.id}">
            <div class="class-card-header">
                <div>
                    <div class="class-card-title">
                        <i class="fas fa-graduation-cap"></i>
                        ${escapeHtml(classItem.name)}
                    </div>
                    <div class="class-card-meta">
                        <span>
                            <i class="fas fa-calendar"></i>
                            ${formatDate(classItem.date)}
                        </span>
                        <span>
                            <i class="fas fa-clock"></i>
                            ${formatTime(classItem.time)}
                        </span>
                    </div>
                </div>
            </div>
            
            ${classItem.description ? `
                <div class="class-card-description">
                    <i class="fas fa-align-left"></i>
                    ${escapeHtml(classItem.description)}
                </div>
            ` : ''}
            
            <div class="class-card-footer">
                <div class="athlete-count">
                    <i class="fas fa-users"></i>
                    ${classItem.athleteCount || 0} Athletes
                </div>
                <button class="btn btn-secondary" onclick="viewRoster('${classItem.id}')">
                    <i class="fas fa-list-ul"></i>
                    View Roster
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Format time for display
 */
function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
    if (show) {
        loadingIndicator.classList.remove('hidden');
    } else {
        loadingIndicator.classList.add('hidden');
    }
}

/**
 * Show notification (placeholder for now)
 */
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    // Add toast styles if not already added
    if (!document.querySelector('#toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            .toast {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 1001;
                animation: slideIn 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            }
            .toast-success { background: linear-gradient(135deg, #4CAF50, #45a049); }
            .toast-error { background: linear-gradient(135deg, #f44336, #d32f2f); }
            .toast-info { background: linear-gradient(135deg, #2196F3, #1976D2); }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(toast);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * View roster for a class (placeholder function)
 */
function viewRoster(classId) {
    const classItem = currentClasses.find(c => c.id === classId);
    if (classItem) {
        showNotification(`View Roster for "${classItem.name}" - Coming Soon!`, 'info');
    }
}

// Make viewRoster available globally for onclick handlers
window.viewRoster = viewRoster;
