/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

/* Navigation Bar */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #764ba2;
}

.nav-brand i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    color: #666;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.nav-link:hover {
    background: rgba(118, 75, 162, 0.1);
    color: #764ba2;
    transform: translateY(-2px);
}

.nav-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
}

/* Main Content */
.main-content {
    height: calc(100vh - 80px);
    overflow-y: auto;
    padding: 2rem;
}

.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Form Styles */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    color: #764ba2;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.form-container h2 i {
    margin-right: 0.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #555;
    display: flex;
    align-items: center;
}

.form-group label i {
    margin-right: 0.5rem;
    color: #764ba2;
}

.form-group input,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #764ba2;
    box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
    background: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Button Styles */
.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(118, 75, 162, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #764ba2;
    border: 2px solid #764ba2;
}

.btn-secondary:hover {
    background: #764ba2;
    color: white;
    transform: translateY(-2px);
}

/* Classes Container */
.classes-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.classes-container h2 {
    color: #764ba2;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.classes-container h2 i {
    margin-right: 0.5rem;
}

.classes-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Class Card Styles */
.class-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #764ba2;
}

.class-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.class-card-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.25rem;
}

.class-card-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.class-card-meta span {
    display: flex;
    align-items: center;
}

.class-card-meta i {
    margin-right: 0.25rem;
    color: #764ba2;
}

.class-card-description {
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.class-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.athlete-count {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Coming Soon Pages */
.coming-soon {
    text-align: center;
    color: white;
    padding: 4rem 2rem;
}

.coming-soon i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.coming-soon h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.coming-soon p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Loading Indicator */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 1000;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.classes-list::-webkit-scrollbar {
    width: 8px;
}

.classes-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.classes-list::-webkit-scrollbar-thumb {
    background: rgba(118, 75, 162, 0.3);
    border-radius: 10px;
}

.classes-list::-webkit-scrollbar-thumb:hover {
    background: rgba(118, 75, 162, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
    }
    
    .nav-menu {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .class-card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .class-card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
