/* Import Professional Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #FFFFFF;
    color: #000000;
    height: 100vh;
    overflow: hidden;
    font-weight: 500;
}

/* Navigation Bar */
.navbar {
    background: #000000;
    padding: 1.25rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 3px solid #FF0000;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-brand i {
    margin-right: 0.75rem;
    font-size: 2rem;
    color: #FF0000;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    text-decoration: none;
    color: #FFFFFF;
    border-radius: 0;
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.nav-link:hover {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
    transform: translateY(-1px);
}

.nav-link.active {
    background: #0033FF;
    color: #FFFFFF;
    border-color: #0033FF;
}

/* Main Content */
.main-content {
    height: calc(100vh - 95px);
    overflow-y: auto;
    padding: 2rem;
    background: #FFFFFF;
}

.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    background: #000000;
    color: #FFFFFF;
    border-radius: 0;
    border: 3px solid #FF0000;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.page-header p {
    font-size: 1.3rem;
    font-weight: 600;
    color: #FF0000;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Form Styles */
.form-container {
    background: #FFFFFF;
    border: 3px solid #0033FF;
    border-radius: 0;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2);
}

.form-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #000000;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.form-group label i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.form-group input,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #000000;
    border-radius: 0;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #FFFFFF;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    pointer-events: auto;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    background: #FFFFFF;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Specific fix for className input */
#className {
    pointer-events: auto !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    cursor: text !important;
}

/* Athlete Form Styling */
.athlete-form-container {
    border-color: #0033FF !important;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2) !important;
}

.athlete-form-container h2 {
    color: #0033FF !important;
}

.athlete-form-container h2 i {
    color: #FF0000 !important;
}

/* File Input Styling */
.file-input {
    position: relative;
    background: #FFFFFF;
    border: 2px solid #000000;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input:hover {
    border-color: #0033FF;
    background: #F8F9FA;
}

.file-input:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
}

/* File input custom styling */
.file-input::file-selector-button {
    background: #FF0000;
    color: #FFFFFF;
    border: none;
    padding: 0.5rem 1rem;
    margin-right: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input::file-selector-button:hover {
    background: #000000;
}

/* Button Styles */
.btn {
    padding: 1rem 2.5rem;
    border: 3px solid transparent;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Inter', sans-serif;
}

.btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
}

.btn-primary:hover {
    background: #000000;
    border-color: #000000;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background: #FFFFFF;
    color: #0033FF;
    border-color: #0033FF;
}

.btn-secondary:hover {
    background: #0033FF;
    color: #FFFFFF;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 51, 255, 0.3);
}

/* Classes Container */
.classes-container {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    border-radius: 0;
    padding: 2.5rem;
    box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.classes-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.classes-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.classes-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Class Card Styles */
.class-card {
    background: #FFFFFF;
    border: 2px solid #000000;
    border-radius: 0;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border-left: 6px solid #0033FF;
}

.class-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border-left-color: #FF0000;
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.class-card-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: #000000;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.class-card-meta {
    display: flex;
    gap: 1.5rem;
    font-size: 1rem;
    color: #000000;
    margin-bottom: 1rem;
    font-weight: 600;
}

.class-card-meta span {
    display: flex;
    align-items: center;
}

.class-card-meta i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.class-card-description {
    color: #000000;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-weight: 500;
}

.class-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.athlete-count {
    background: #000000;
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid #000000;
}

/* Coming Soon Pages */
.coming-soon {
    text-align: center;
    color: #FFFFFF;
    padding: 6rem 2rem;
    background: #000000;
    border: 3px solid #FF0000;
    border-radius: 0;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.coming-soon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 0, 0.1), transparent);
    animation: sweep 3s infinite;
}

.coming-soon i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: #FF0000;
    animation: pulse 2s infinite;
}

.coming-soon h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: glitch 4s infinite;
}

.coming-soon p {
    font-size: 1.3rem;
    font-weight: 600;
    color: #0033FF;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: blink 2s infinite;
}

/* Animations */
@keyframes sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes glitch {
    0%, 90%, 100% { transform: translateX(0); }
    10% { transform: translateX(-2px); }
    20% { transform: translateX(2px); }
    30% { transform: translateX(-1px); }
    40% { transform: translateX(1px); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.7; }
}

/* Loading Indicator */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    z-index: 1000;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #0033FF;
    border-top: 4px solid #FF0000;
    border-radius: 0;
    animation: spin 1s linear infinite;
    margin-bottom: 2rem;
}

.loading p {
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.classes-list::-webkit-scrollbar {
    width: 12px;
}

.classes-list::-webkit-scrollbar-track {
    background: #FFFFFF;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb {
    background: #FF0000;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb:hover {
    background: #0033FF;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #000000;
}

.empty-state i {
    font-size: 4rem;
    color: #FF0000;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 0;
    color: #FFFFFF;
    font-weight: 700;
    z-index: 1001;
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
}

.toast-success {
    background: #000000;
    border-color: #00FF00;
    color: #00FF00;
}

.toast-error {
    background: #000000;
    border-color: #FF0000;
    color: #FF0000;
}

.toast-info {
    background: #000000;
    border-color: #0033FF;
    color: #0033FF;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-menu {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
        height: calc(100vh - 140px);
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .class-card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .class-card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .coming-soon {
        padding: 3rem 1rem;
    }

    .coming-soon h2 {
        font-size: 2rem;
    }

    .athlete-form-container {
        margin-bottom: 1rem;
    }

    .file-input::file-selector-button {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }
}
