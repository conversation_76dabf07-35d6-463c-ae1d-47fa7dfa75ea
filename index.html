<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ninja Score Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-brand">
            <i class="fas fa-user-ninja"></i>
            <span>Ninja Score Tracker</span>
        </div>
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#" class="nav-link active" data-page="home">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="leaderboard">
                    <i class="fas fa-trophy"></i>
                    <span>Leaderboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="stats">
                    <i class="fas fa-chart-bar"></i>
                    <span>Stats</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="run">
                    <i class="fas fa-play"></i>
                    <span>Run</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Home Page -->
        <div id="home-page" class="page active">
            <div class="page-header">
                <h1><i class="fas fa-home"></i> Home</h1>
                <p>Manage your Ninja Classes</p>
            </div>

            <!-- Add New Athlete Form -->
            <div class="form-container athlete-form-container">
                <h2><i class="fas fa-user-plus"></i> Add New Athlete</h2>
                <form id="add-athlete-form-home">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteNameHome">
                                <i class="fas fa-user"></i> Full Name
                            </label>
                            <input type="text" id="athleteNameHome" name="athleteName" required
                                   placeholder="Enter athlete's full name...">
                        </div>
                        <div class="form-group">
                            <label for="athleteAgeHome">
                                <i class="fas fa-birthday-cake"></i> Age
                            </label>
                            <input type="number" id="athleteAgeHome" name="athleteAge" min="5" max="99" required
                                   placeholder="Age">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteNicknameHome">
                                <i class="fas fa-tag"></i> Nickname (Optional)
                            </label>
                            <input type="text" id="athleteNicknameHome" name="athleteNickname"
                                   placeholder="e.g., Flash, Shadow, Lightning...">
                        </div>
                        <div class="form-group">
                            <label for="athleteLevelHome">
                                <i class="fas fa-star"></i> Level
                            </label>
                            <select id="athleteLevelHome" name="athleteLevel" required>
                                <option value="">Select Level</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteAvatarHome">
                                <i class="fas fa-image"></i> Avatar (Optional)
                            </label>
                            <input type="file" id="athleteAvatarHome" name="athleteAvatar"
                                   accept="image/*" class="file-input">
                        </div>
                        <div class="form-group">
                            <!-- Empty space for layout balance -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="athleteDescriptionHome">
                            <i class="fas fa-align-left"></i> Description (Optional)
                        </label>
                        <textarea id="athleteDescriptionHome" name="athleteDescription"
                                  placeholder="Brief description about the athlete..." rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Add Athlete
                    </button>
                </form>
            </div>

            <!-- Add New Class Form -->
            <div class="form-container">
                <h2><i class="fas fa-plus-circle"></i> Add New Ninja Class</h2>
                <form id="add-class-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="className">
                                <i class="fas fa-tag"></i> Class Name
                            </label>
                            <input type="text" id="className" name="className" required 
                                   placeholder="Enter class name...">
                        </div>
                        <div class="form-group">
                            <label for="classDate">
                                <i class="fas fa-calendar"></i> Date
                            </label>
                            <input type="date" id="classDate" name="classDate" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="classTime">
                                <i class="fas fa-clock"></i> Time
                            </label>
                            <input type="time" id="classTime" name="classTime" required>
                        </div>
                        <div class="form-group">
                            <label for="classDescription">
                                <i class="fas fa-align-left"></i> Description
                            </label>
                            <textarea id="classDescription" name="classDescription" 
                                      placeholder="Enter class description..." rows="3"></textarea>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Class
                    </button>
                </form>
            </div>

            <!-- Classes List -->
            <div class="classes-container">
                <h2><i class="fas fa-list"></i> Your Classes</h2>
                <div id="classes-list" class="classes-list">
                    <!-- Classes will be dynamically loaded here -->
                </div>
            </div>
        </div>

        <!-- Leaderboard Page -->
        <div id="leaderboard-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-trophy"></i> Leaderboard</h1>
                <p>Top Performing Ninja Athletes</p>
            </div>

            <!-- Leaderboard Container -->
            <div class="leaderboard-container">
                <div class="leaderboard-header">
                    <h2><i class="fas fa-medal"></i> Rankings</h2>
                    <div class="leaderboard-stats">
                        <span id="total-athletes-lb">0 Athletes</span>
                        <span class="separator">•</span>
                        <span id="last-updated-lb">Last Updated: Now</span>
                    </div>
                </div>

                <!-- Leaderboard List -->
                <div id="leaderboard-list" class="leaderboard-list">
                    <!-- Athletes will be dynamically loaded here -->
                </div>

                <!-- Empty State -->
                <div id="empty-leaderboard" class="empty-state hidden">
                    <i class="fas fa-trophy"></i>
                    <h3>No Athletes Yet</h3>
                    <p>Add athletes from the Home page to see them on the leaderboard!</p>
                </div>
            </div>
        </div>

        <!-- Stats Page -->
        <div id="stats-page" class="page">
            <div class="coming-soon">
                <i class="fas fa-chart-bar"></i>
                <h2>🚧 Coming Soon</h2>
                <p>Statistics and analytics will be available in a future update.</p>
            </div>
        </div>

        <!-- Run Page -->
        <div id="run-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-play"></i> Live Run Session</h1>
                <p>Track Scores & Performance During Class</p>
            </div>

            <!-- Class Selector Section -->
            <div class="run-class-selector">
                <h2><i class="fas fa-graduation-cap"></i> Select Class</h2>
                <div class="class-selector-container">
                    <select id="run-class-select" class="class-select">
                        <option value="">Choose a class to start tracking...</option>
                    </select>
                    <button id="load-class-btn" class="btn btn-primary" disabled>
                        <i class="fas fa-users"></i>
                        Load Athletes
                    </button>
                </div>
            </div>

            <!-- Selected Class Info -->
            <div id="selected-class-info" class="selected-class-info hidden">
                <div class="class-info-card">
                    <div class="class-details">
                        <h3 id="selected-class-name">Class Name</h3>
                        <div class="class-meta-info">
                            <span id="selected-class-date">
                                <i class="fas fa-calendar"></i>
                                Date
                            </span>
                            <span id="selected-class-time">
                                <i class="fas fa-clock"></i>
                                Time
                            </span>
                            <span id="selected-athlete-count">
                                <i class="fas fa-users"></i>
                                0 Athletes
                            </span>
                        </div>
                    </div>
                    <div class="session-actions">
                        <button id="start-session-btn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            Start Session
                        </button>
                        <button id="end-session-btn" class="btn btn-secondary hidden">
                            <i class="fas fa-stop"></i>
                            End Session
                        </button>
                    </div>
                </div>
            </div>

            <!-- Athletes Tracking Grid -->
            <div id="athletes-tracking-section" class="athletes-tracking-section hidden">
                <div class="tracking-header">
                    <h2><i class="fas fa-stopwatch"></i> Live Tracking</h2>
                    <div class="session-timer">
                        <i class="fas fa-clock"></i>
                        <span id="session-timer">00:00</span>
                    </div>
                </div>

                <div id="athletes-tracking-grid" class="athletes-tracking-grid">
                    <!-- Athlete tracking cards will be dynamically loaded here -->
                </div>

                <!-- Session Summary -->
                <div class="session-summary">
                    <button id="save-all-scores-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save All Scores
                    </button>
                    <button id="clear-all-scores-btn" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i>
                        Clear All
                    </button>
                </div>
            </div>

            <!-- Empty State -->
            <div id="run-empty-state" class="empty-state">
                <i class="fas fa-play-circle"></i>
                <h3>Ready to Start Tracking</h3>
                <p>Select a class above to begin tracking athlete performance during your session.</p>
            </div>
        </div>

        <!-- Settings Page -->
        <div id="settings-page" class="page">
            <div class="coming-soon">
                <i class="fas fa-cog"></i>
                <h2>🚧 Coming Soon</h2>
                <p>Application settings will be available in a future update.</p>
            </div>
        </div>
    </main>

    <!-- Loading Indicator -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- JavaScript -->
    <script src="renderer.js"></script>
</body>
</html>
