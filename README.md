# 🥷 Ninja Score Tracker

An offline desktop application built with Electron.js for tracking ninja class scores and managing athletes.

## 🚀 Features

### Current Features (v1.0)
- **Home Page**: Fully functional class management
  - Add new ninja classes with name, date, time, and description
  - View all created classes in a scrollable list
  - Auto-save data to local JSON file
  - Offline-first design - no internet required

- **Roster Management**: Complete athlete management system
  - View and manage athletes enrolled in each class
  - Add new athletes with detailed profiles (name, age, nickname, level, description)
  - Add existing athletes from other classes
  - Remove athletes from specific classes
  - Visual athlete cards with avatars, stats, and progress tracking
  - Search and filter functionality for large rosters

### Coming Soon
- **Leaderboard**: View top performers and rankings
- **Stats**: Analytics and performance insights
- **Run**: Execute classes and record scores
- **Settings**: App configuration and preferences

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm (comes with Node.js)

### Installation
1. Clone or download this project
2. Open terminal in the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Running the App
```bash
npm start
```

Or for development mode:
```bash
npm run dev
```

## 📁 Project Structure

```
ninja-score-tracker/
├── main.js              # Main Electron process
├── index.html           # Main UI layout
├── roster.html          # Athlete roster management page
├── styles.css           # Main application styling
├── roster-styles.css    # Roster page specific styling
├── renderer.js          # Main page renderer logic
├── roster.js            # Roster page renderer logic
├── package.json         # Project configuration
├── classes.json         # Class data storage (auto-created)
├── athletes.json        # Athlete data storage (auto-created)
└── README.md            # This file
```

## 💾 Data Storage

- All class data is stored locally in `classes.json`
- Data persists between app sessions
- No internet connection required
- Automatic backup on each save

## 🎨 UI Features

- **Bold Professional Design**: High-contrast black, white, red, and blue color scheme
- **Powerful Aesthetic**: Sharp edges, strong typography, and "badass" styling
- **Responsive Layout**: Works on different screen sizes
- **Icon Integration**: Font Awesome icons with brand color accents
- **Smooth Animations**: Hover effects, glitch animations, and transitions
- **Toast Notifications**: Bold system notifications with brand colors
- **Coming Soon Pages**: Animated dark backgrounds with glitch effects

## 🔧 Technical Details

### Built With
- **Electron.js**: Desktop app framework
- **HTML5**: Structure and layout
- **CSS3**: Bold styling with brand colors, animations, and effects
- **Inter Font**: Professional typography from Google Fonts
- **Vanilla JavaScript**: No external frameworks
- **Node.js fs module**: File system operations

### Architecture
- **Main Process**: Handles file operations and window management
- **Renderer Process**: Manages UI interactions and display
- **IPC Communication**: Secure data exchange between processes

## 📝 Usage

### Adding a New Class
1. Navigate to the Home page
2. Fill in the class details:
   - **Class Name**: Required field
   - **Date**: Required field (defaults to today)
   - **Time**: Required field
   - **Description**: Optional field
3. Click "Add Class" button
4. Class will appear in the list below

### Viewing Classes
- All classes are displayed in chronological order (newest first)
- Each class card shows:
  - Class name and description
  - Date and time
  - Current athlete count
  - "View Roster" button to manage class athletes

### Managing Athletes
1. Click "View Roster" on any class card
2. **Add New Athlete**:
   - Fill in athlete details (name, age, nickname, level, description)
   - Athlete is automatically enrolled in the current class
3. **Add Existing Athlete**:
   - Search through athletes from other classes
   - Add them to the current class roster
4. **Remove Athletes**:
   - Remove athletes from the current class (doesn't delete the athlete)
   - Athletes can still be enrolled in other classes

## 🔮 Future Development

The app is designed with extensibility in mind. Future updates will include:

- **Athlete Management**: Add/remove athletes from classes
- **Score Tracking**: Record and manage performance scores
- **Leaderboards**: Rankings and competitions
- **Statistics**: Performance analytics and trends
- **Export Features**: PDF reports and data export
- **Backup/Restore**: Cloud sync and data backup

## 🐛 Troubleshooting

### App Won't Start
- Ensure Node.js is installed: `node --version`
- Reinstall dependencies: `npm install`
- Check for error messages in terminal

### Data Not Saving
- Check file permissions in project directory
- Ensure `classes.json` can be created/modified
- Look for error messages in developer console

### UI Issues
- Try refreshing the app (Ctrl+R or Cmd+R)
- Check browser compatibility (Electron uses Chromium)

## 📄 License

MIT License - Feel free to modify and distribute.

## 🤝 Contributing

This is a personal project, but suggestions and improvements are welcome!

---

**Version**: 1.0.0  
**Last Updated**: 2025-06-17  
**Status**: Active Development
