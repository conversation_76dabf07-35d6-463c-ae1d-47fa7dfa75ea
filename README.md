# 🥷 Ninja Score Tracker

An offline desktop application built with Electron.js for tracking ninja class scores and managing athletes.

## 🚀 Features

### Current Features (v1.0)
- **Home Page**: Fully functional class management
  - Add new ninja classes with name, date, time, and description
  - View all created classes in a scrollable list
  - Auto-save data to local JSON file
  - Offline-first design - no internet required

### Coming Soon
- **Leaderboard**: View top performers and rankings
- **Stats**: Analytics and performance insights
- **Run**: Execute classes and record scores
- **Settings**: App configuration and preferences

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm (comes with Node.js)

### Installation
1. Clone or download this project
2. Open terminal in the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Running the App
```bash
npm start
```

Or for development mode:
```bash
npm run dev
```

## 📁 Project Structure

```
ninja-score-tracker/
├── main.js           # Main Electron process
├── index.html        # Main UI layout
├── styles.css        # Application styling
├── renderer.js       # Renderer process logic
├── package.json      # Project configuration
├── classes.json      # Data storage (auto-created)
└── README.md         # This file
```

## 💾 Data Storage

- All class data is stored locally in `classes.json`
- Data persists between app sessions
- No internet connection required
- Automatic backup on each save

## 🎨 UI Features

- **Modern Design**: Clean, gradient-based interface
- **Responsive Layout**: Works on different screen sizes
- **Icon Integration**: Font Awesome icons throughout
- **Smooth Animations**: Hover effects and transitions
- **Toast Notifications**: User feedback for actions

## 🔧 Technical Details

### Built With
- **Electron.js**: Desktop app framework
- **HTML5**: Structure and layout
- **CSS3**: Styling with gradients and animations
- **Vanilla JavaScript**: No external frameworks
- **Node.js fs module**: File system operations

### Architecture
- **Main Process**: Handles file operations and window management
- **Renderer Process**: Manages UI interactions and display
- **IPC Communication**: Secure data exchange between processes

## 📝 Usage

### Adding a New Class
1. Navigate to the Home page
2. Fill in the class details:
   - **Class Name**: Required field
   - **Date**: Required field (defaults to today)
   - **Time**: Required field
   - **Description**: Optional field
3. Click "Add Class" button
4. Class will appear in the list below

### Viewing Classes
- All classes are displayed in chronological order (newest first)
- Each class card shows:
  - Class name and description
  - Date and time
  - Current athlete count (starts at 0)
  - "View Roster" button (placeholder for future feature)

## 🔮 Future Development

The app is designed with extensibility in mind. Future updates will include:

- **Athlete Management**: Add/remove athletes from classes
- **Score Tracking**: Record and manage performance scores
- **Leaderboards**: Rankings and competitions
- **Statistics**: Performance analytics and trends
- **Export Features**: PDF reports and data export
- **Backup/Restore**: Cloud sync and data backup

## 🐛 Troubleshooting

### App Won't Start
- Ensure Node.js is installed: `node --version`
- Reinstall dependencies: `npm install`
- Check for error messages in terminal

### Data Not Saving
- Check file permissions in project directory
- Ensure `classes.json` can be created/modified
- Look for error messages in developer console

### UI Issues
- Try refreshing the app (Ctrl+R or Cmd+R)
- Check browser compatibility (Electron uses Chromium)

## 📄 License

MIT License - Feel free to modify and distribute.

## 🤝 Contributing

This is a personal project, but suggestions and improvements are welcome!

---

**Version**: 1.0.0  
**Last Updated**: 2025-06-17  
**Status**: Active Development
